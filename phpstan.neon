parameters:
    level: 8
    paths:
        - src
    excludePaths:
        - vendor
    ignoreErrors:
        # Allow mixed types for Smarty template variables and parameters
        - '#Parameter \#\d+ \$params of method .+::execute\(\) expects array, mixed given\.#'
        - '#Cannot access offset .+ on mixed\.#'
        - '#Cannot call method .+ on mixed\.#'
        # Allow $_SERVER superglobal usage
        - '#Cannot access offset .+ on array\|false\.#'
    checkMissingIterableValueType: false
    checkGenericClassInNonGenericObjectType: false
    reportUnmatchedIgnoredErrors: false
